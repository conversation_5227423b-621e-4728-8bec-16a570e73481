const { contextBridge, ipc<PERSON><PERSON><PERSON> } = require('electron');

// Expose protected methods that allow the renderer process to use
// the ipc<PERSON>enderer without exposing the entire object
contextBridge.exposeInMainWorld('electronAPI', {
  // File operations
  selectFile: () => ipcRenderer.invoke('select-file'),
  processFile: (filePath) => ipcRenderer.invoke('process-file', filePath),
  
  // Question generation
  generateQuestions: (content, type, count, preferredModel) => ipcRenderer.invoke('generate-questions', content, type, count, preferredModel),
  
  // Quiz operations
  startQuiz: (questions) => ipcRenderer.invoke('start-quiz', questions),
  submitAnswer: (questionIndex, answer) => ipcRenderer.invoke('submit-answer', questionIndex, answer),
  getQuizResults: () => ipcRenderer.invoke('get-quiz-results'),
  
  // Database operations
  saveQuizSession: (session) => ipc<PERSON>enderer.invoke('save-quiz-session', session),
  getQuizHistory: () => ipcRenderer.invoke('get-quiz-history'),
  getStatistics: () => ipcRenderer.invoke('get-statistics'),

  // Saved Quiz operations
  saveSavedQuiz: (savedQuiz) => ipcRenderer.invoke('save-saved-quiz', savedQuiz),
  getSavedQuizzes: () => ipcRenderer.invoke('get-saved-quizzes'),
  deleteSavedQuiz: (quizId) => ipcRenderer.invoke('delete-saved-quiz', quizId),
  clearAllSavedQuizzes: () => ipcRenderer.invoke('clear-all-saved-quizzes'),
  fixIslamicDates: () => ipcRenderer.invoke('fix-islamic-dates'),
  
  // API Key Management
  getApiKeyInfo: () => ipcRenderer.invoke('get-api-key-info'),
  updateApiKey: (newApiKey) => ipcRenderer.invoke('update-api-key', newApiKey),
  testApiKey: (testKey) => ipcRenderer.invoke('test-api-key', testKey),

  // Settings
  getSettings: () => ipcRenderer.invoke('get-settings'),
  saveSettings: (settings) => ipcRenderer.invoke('save-settings', settings),
  
  // Feedback
  submitFeedback: (feedback) => ipcRenderer.invoke('submit-feedback', feedback),
  
  // Menu events
  onMenuAction: (callback) => ipcRenderer.on('menu-action', callback),
  onFileSelected: (callback) => ipcRenderer.on('file-selected', callback),
  
  // Menu specific events
  onNewQuiz: (callback) => ipcRenderer.on('menu-new-quiz', callback),
  onGenerateMCQ: (callback) => ipcRenderer.on('menu-generate-mcq', callback),
  onGenerateTF: (callback) => ipcRenderer.on('menu-generate-tf', callback),
  onStartQuiz: (callback) => ipcRenderer.on('menu-start-quiz', callback),

  onStatistics: (callback) => ipcRenderer.on('menu-statistics', callback),
  onHistory: (callback) => ipcRenderer.on('menu-history', callback),
  onAbout: (callback) => ipcRenderer.on('menu-about', callback),
  onHelp: (callback) => ipcRenderer.on('menu-help', callback),
  
  // Progress updates
  onProgressUpdate: (callback) => ipcRenderer.on('progress-update', callback),
  
  // Error handling
  onError: (callback) => ipcRenderer.on('error', callback),
  
  // Export functions
  saveFile: (options) => ipcRenderer.invoke('save-file', options),
  savePDF: (filePath, htmlContent) => ipcRenderer.invoke('save-pdf', filePath, htmlContent),

  // Model Management
  addModel: (modelData) => ipcRenderer.invoke('add-model', modelData),
  removeModel: (modelId) => ipcRenderer.invoke('remove-model', modelId),
  getAllModels: () => ipcRenderer.invoke('get-all-models'),
  testModel: (modelId, content, questionType, questionCount) => ipcRenderer.invoke('test-model', modelId, content, questionType, questionCount),
  runModelSimulation: (testParams) => ipcRenderer.invoke('run-model-simulation', testParams),
  testTrueFalseLogic: (testQuestions) => ipcRenderer.invoke('test-tf-logic', testQuestions),

  // Rate Limit Management
  getRateLimitedModels: () => ipcRenderer.invoke('get-rate-limited-models'),
  clearModelRateLimit: (modelId) => ipcRenderer.invoke('clear-model-rate-limit', modelId),
  clearAllRateLimits: () => ipcRenderer.invoke('clear-all-rate-limits'),

  // Utility functions
  showNotification: (title, body) => ipcRenderer.invoke('show-notification', title, body),
  openExternal: (url) => ipcRenderer.invoke('open-external', url),
  
  // Remove listeners
  removeAllListeners: (channel) => ipcRenderer.removeAllListeners(channel)
});

// Expose some Node.js APIs that are safe to use in the renderer
contextBridge.exposeInMainWorld('nodeAPI', {
  platform: process.platform,
  versions: process.versions
});

// Expose application info
contextBridge.exposeInMainWorld('appInfo', {
  name: 'MCQ & TF Question Generator',
  version: '1.0.0',
  description: 'Desktop application for generating multiple-choice and true/false questions'
});
