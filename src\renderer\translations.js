// Language translations for the MCQ & TF Generator
const translations = {
    en: {
        // Header
        appTitle: "MCQ & TF Generator",
        toggleTheme: "Toggle Dark/Light Mode",
        switchLanguage: "Switch Language",
        history: "History",
        statistics: "Statistics",
        backToMain: "Back to Main",
        
        // Welcome Screen
        welcomeTitle: "Welcome to Question Generator",
        welcomeSubtitle: "Generate multiple-choice and true/false questions from your educational content",
        chooseQuestionType: "Choose Question Type",
        multipleChoice: "Multiple Choice (MCQ)",
        multipleChoiceDesc: "Generate questions with multiple options",
        trueFalse: "True/False (TF)",
        trueFalseDesc: "Generate true or false questions",
        
        // Content Input
        addYourContent: "Add Your Content",
        typeText: "Type Text",
        uploadFile: "Upload File",
        uploadImage: "Upload Image",
        
        // AI Model Selection
        aiModelSelection: "AI Model Selection",
        aiModelSelectionDesc: "Choose your preferred AI model for question generation",
        preferredAiModel: "Preferred AI Model",
        autoBestAvailable: "Auto (Best Available)",
        checkingAvailability: "Checking availability...",
        modelStatusWillAppear: "Model status will appear here",
        modelAvailable: "Model available",
        modelRateLimited: "Rate Limited",
        modelTemporarilyUnavailable: "Model temporarily unavailable due to rate limits",
        
        // Model Management
        modelManagement: "Model Management",
        modelManagementDesc: "Manage your AI models and API configuration",
        aiModels: "AI Models",
        addModel: "Add Model",
        addNewAiModel: "Add new AI model",
        removeModel: "Remove Model",
        deleteExistingModel: "Delete existing model",

        toolsTesting: "Tools & Testing",
        testModels: "Test Models",
        verifyFunctionality: "Verify functionality",
        apiKey: "API Key",
        manageCredentials: "Manage credentials",
        
        // Question Settings
        questionCountSettings: "Question Count Settings",
        questionCountSettingsDesc: "Configure how many questions to generate",
        questionsPerPage: "Questions per Page",
        questionsPerPageDesc: "Number of questions to generate per page of multi-page documents",
        questionsPerImage: "Questions per Image",
        questionsPerImageDesc: "Number of questions to generate from uploaded images",
        
        // Content Screen
        addContent: "Add Content",
        back: "Back",
        enterContentPlaceholder: "Enter your educational content here...",
        generateQuestions: "Generate Questions",
        dragDropFile: "Drag and drop your file here or click to browse",
        supportedFiles: "Supported: PDF, DOCX, DOC, TXT",
        dragDropImage: "Drag and drop your image here or click to browse",
        supportedImages: "Supported: JPG, PNG, BMP, TIFF",
        
        // Processing Screen
        aiQuestionGenerator: "AI Question Generator",
        initializingAiModels: "Initializing AI models...",
        calculating: "Calculating...",
        analyzingContent: "Analyzing Content",
        processingAiModels: "Processing AI Models",
        generatingQuestions: "Generating Questions",
        finalizing: "Finalizing",
        
        // Questions Display
        generatedQuestions: "Generated Questions",
        startQuiz: "Start Quiz",
        showQuestionsWithAnswers: "Show Questions with Answers",
        exportPdf: "Export PDF",
        mainMenu: "Main Menu",
        
        // Quiz Screen
        question: "Question",
        of: "of",
        score: "Score",
        submitAnswer: "Submit Answer",
        nextQuestion: "Next Question",
        finishQuiz: "Finish Quiz",
        
        // Results Screen
        quizResults: "Quiz Results",
        
        // Buttons and Actions
        close: "Close",
        cancel: "Cancel",
        confirm: "Confirm",
        save: "Save",
        delete: "Delete",
        edit: "Edit",
        refresh: "Refresh",
        
        // Model Management Dialogs
        addNewModel: "Add New AI Model",
        modelId: "Model ID",
        modelIdPlaceholder: "e.g., openai/gpt-4:free",
        modelIdHelp: "Enter the full model identifier (provider/model-name:tier)",
        displayName: "Display Name",
        displayNamePlaceholder: "e.g., GPT-4 (Free)",
        displayNameHelp: "Friendly name to display in the dropdown",
        description: "Description (Optional)",
        descriptionPlaceholder: "e.g., Advanced reasoning model",
        descriptionHelp: "Brief description of the model's capabilities",
        
        removeAiModel: "Remove AI Model",
        selectModelToRemove: "Select Model to Remove:",
        chooseModelToRemove: "Choose a model to remove...",
        removeWarning: "⚠️ Warning: This will permanently remove the model from your list. You can remove ANY model including all default models.",
        
        allAiModels: "All AI Models",
        loadingModels: "Loading models...",
        
        modelTestingSimulation: "Model Testing & Simulation",
        testContent: "Test Content",
        testContentPlaceholder: "Enter test content for question generation...",
        testContentDefault: "The human heart has four chambers: two atria and two ventricles. Blood flows from the right atrium to the right ventricle, then to the lungs for oxygenation.",
        questionType: "Question Type",
        questionCount: "Question Count",
        startTestingAllModels: "Start Testing All Models",
        stopTesting: "Stop Testing",
        clearRateLimits: "Clear Rate Limits",
        clickStartTesting: "Click \"Start Testing\" to test all available models",
        exportResults: "Export Results",
        
        // API Key Manager
        apiKeyManager: "API Key Manager",
        currentApiKey: "Current API Key Status",
        newApiKey: "Update API Key",
        enterNewApiKey: "New OpenRouter API Key:",
        updateApiKey: "Update API Key",
        apiKeyUpdated: "API key updated successfully!",
        testResults: "Test Results",
        quickInstructions: "Quick Instructions",

        // Modal Dialog Actions
        close: "Close",
        cancel: "Cancel",
        refresh: "Refresh",

        // Add Model Dialog
        addNewAiModel: "Add New AI Model",
        modelId: "Model ID:",
        modelIdHelp: "Enter the full model identifier (provider/model-name:tier)",
        displayName: "Display Name:",
        displayNameHelp: "Friendly name to display in the dropdown",
        descriptionOptional: "Description (Optional):",
        descriptionHelp: "Brief description of the model's capabilities",

        // Remove Model Dialog
        removeAiModel: "Remove AI Model",
        selectModelToRemove: "Select Model to Remove:",
        chooseModelToRemove: "Choose a model to remove...",
        removeModelWarning: "⚠️ Warning: This will permanently remove the model from your list. You can remove ANY model including all default models.",

        // View Models Dialog
        allAiModels: "All AI Models",
        loadingModels: "Loading models...",

        // Test Models Dialog
        modelTestingSimulation: "Model Testing & Simulation",
        testContent: "Test Content:",
        testContentPlaceholder: "Enter test content for question generation...",
        multipleChoiceMcq: "Multiple Choice (MCQ)",
        trueFalseTf: "True/False (TF)",
        startTestingAllModels: "Start Testing All Models",
        stopTesting: "Stop Testing",
        clickStartTesting: "Click \"Start Testing\" to test all available models",

        // History & Statistics
        quizHistory: "Quiz History",
        clearHistory: "Clear History",
        dateRange: "Date Range",
        allTypes: "All Types",
        allTime: "All Time",
        today: "Today",
        thisWeek: "This Week",
        thisMonth: "This Month",
        totalQuizzes: "Total Quizzes",
        averageScore: "Average Score",
        averageTime: "Average Time",
        loadingQuizHistory: "Loading quiz history...",
        savedQuizzes: "Saved Quizzes",
        completedQuizzes: "Completed Quizzes",
        loadingSavedQuizzes: "Loading saved quizzes...",
        takeQuiz: "Take Quiz",
        deleteSavedQuiz: "Delete",
        noSavedQuizzes: "No saved quizzes found",
        clearAllSaved: "Clear All",
        saveQuizTitle: "Save Quiz",
        saveQuizSubtitle: "Give your quiz a memorable name",
        quizNameLabel: "Quiz Name:",
        quizNameHint: "Leave empty to use default name",
        saveQuiz: "Save Quiz",
        cancel: "Cancel",
        source: "Source",
        type: "Type",
        questions: "Questions",
        quizSummaryText: "You are about to save a",
        quizSummaryWith: "quiz with",
        quizSummaryQuestions: "questions from",

        statistics: "Statistics",
        exportStats: "Export Stats",
        overallScore: "Overall Score",
        acrossAllQuizzes: "Across all quizzes",
        currentStreak: "Current Streak",
        daysInARow: "Days in a row",
        performanceMetrics: "Performance Metrics",
        totalQuestions: "Total Questions",
        correctAnswers: "Correct Answers",
        incorrectAnswers: "Incorrect Answers",
        questionTypes: "Question Types",
        mcqQuizzes: "MCQ Quizzes",
        trueFalseQuizzes: "True/False Quizzes",
        activity: "Activity",
        quizzesToday: "Quizzes Today",

        // Quiz Results
        quizResults: "Quiz Results",
        newQuiz: "New Quiz",
        reviewAnswers: "Review Answers",
        saveQuiz: "Save Quiz for Later",

        // Dynamic content translations
        score: "Score",
        correct: "Correct",
        duration: "Duration",
        questions: "Questions",

        // Status Messages
        loading: "Loading...",
        success: "Success",
        error: "Error",
        warning: "Warning",
        info: "Info"
    },
    
    ar: {
        // Header
        appTitle: "مولد الأسئلة متعددة الخيارات والصح والخطأ",
        toggleTheme: "تبديل الوضع المظلم/المضيء",
        switchLanguage: "تغيير اللغة",
        history: "التاريخ",
        statistics: "الإحصائيات",
        backToMain: "العودة للرئيسية",
        
        // Welcome Screen
        welcomeTitle: "مرحباً بك في مولد الأسئلة",
        welcomeSubtitle: "قم بإنشاء أسئلة متعددة الخيارات والصح والخطأ من محتواك التعليمي",
        chooseQuestionType: "اختر نوع السؤال",
        multipleChoice: "متعدد الخيارات",
        multipleChoiceDesc: "إنشاء أسئلة بخيارات متعددة",
        trueFalse: "صح أو خطأ",
        trueFalseDesc: "إنشاء أسئلة صح أو خطأ",
        
        // Content Input
        addYourContent: "أضف محتواك",
        typeText: "اكتب النص",
        uploadFile: "رفع ملف",
        uploadImage: "رفع صورة",
        
        // AI Model Selection
        aiModelSelection: "اختيار نموذج الذكاء الاصطناعي",
        aiModelSelectionDesc: "اختر نموذج الذكاء الاصطناعي المفضل لديك لإنشاء الأسئلة",
        preferredAiModel: "نموذج الذكاء الاصطناعي المفضل",
        autoBestAvailable: "تلقائي (الأفضل المتاح)",
        checkingAvailability: "فحص التوفر...",
        modelStatusWillAppear: "ستظهر حالة النموذج هنا",
        modelAvailable: "النموذج متاح",
        modelRateLimited: "محدود المعدل",
        modelTemporarilyUnavailable: "النموذج غير متاح مؤقتاً بسبب حدود المعدل",
        
        // Model Management
        modelManagement: "إدارة النماذج",
        modelManagementDesc: "إدارة نماذج الذكاء الاصطناعي وإعدادات API",
        aiModels: "نماذج الذكاء الاصطناعي",
        addModel: "إضافة نموذج",
        addNewAiModel: "إضافة نموذج ذكاء اصطناعي جديد",
        removeModel: "حذف نموذج",
        deleteExistingModel: "حذف نموذج موجود",

        toolsTesting: "الأدوات والاختبار",
        testModels: "اختبار النماذج",
        verifyFunctionality: "التحقق من الوظائف",
        apiKey: "مفتاح API",
        manageCredentials: "إدارة بيانات الاعتماد",
        
        // Question Settings
        questionCountSettings: "إعدادات عدد الأسئلة",
        questionCountSettingsDesc: "تكوين عدد الأسئلة المراد إنشاؤها",
        questionsPerPage: "أسئلة لكل صفحة",
        questionsPerPageDesc: "عدد الأسئلة المراد إنشاؤها لكل صفحة من المستندات متعددة الصفحات",
        questionsPerImage: "أسئلة لكل صورة",
        questionsPerImageDesc: "عدد الأسئلة المراد إنشاؤها من الصور المرفوعة",
        
        // Content Screen
        addContent: "إضافة محتوى",
        back: "رجوع",
        enterContentPlaceholder: "أدخل محتواك التعليمي هنا...",
        generateQuestions: "إنشاء الأسئلة",
        dragDropFile: "اسحب وأفلت ملفك هنا أو انقر للتصفح",
        supportedFiles: "المدعوم: PDF, DOCX, DOC, TXT",
        dragDropImage: "اسحب وأفلت صورتك هنا أو انقر للتصفح",
        supportedImages: "المدعوم: JPG, PNG, BMP, TIFF",
        
        // Processing Screen
        aiQuestionGenerator: "مولد الأسئلة بالذكاء الاصطناعي",
        initializingAiModels: "تهيئة نماذج الذكاء الاصطناعي...",
        calculating: "جاري الحساب...",
        analyzingContent: "تحليل المحتوى",
        processingAiModels: "معالجة نماذج الذكاء الاصطناعي",
        generatingQuestions: "إنشاء الأسئلة",
        finalizing: "الانتهاء",
        
        // Questions Display
        generatedQuestions: "الأسئلة المُنشأة",
        startQuiz: "بدء الاختبار",
        showQuestionsWithAnswers: "عرض الأسئلة مع الإجابات",
        exportPdf: "تصدير PDF",
        mainMenu: "القائمة الرئيسية",
        
        // Quiz Screen
        question: "السؤال",
        of: "من",
        score: "النتيجة",
        submitAnswer: "إرسال الإجابة",
        nextQuestion: "السؤال التالي",
        finishQuiz: "إنهاء الاختبار",
        
        // Results Screen
        quizResults: "نتائج الاختبار",
        
        // Buttons and Actions
        close: "إغلاق",
        cancel: "إلغاء",
        confirm: "تأكيد",
        save: "حفظ",
        delete: "حذف",
        edit: "تعديل",
        refresh: "تحديث",
        
        // Model Management Dialogs
        addNewAiModel: "إضافة نموذج ذكاء اصطناعي جديد",
        modelId: "معرف النموذج:",
        modelIdPlaceholder: "مثال: openai/gpt-4:free",
        modelIdHelp: "أدخل معرف النموذج الكامل (المزود/اسم-النموذج:المستوى)",
        displayName: "اسم العرض:",
        displayNamePlaceholder: "مثال: GPT-4 (مجاني)",
        displayNameHelp: "اسم ودود للعرض في القائمة المنسدلة",
        descriptionOptional: "الوصف (اختياري):",
        descriptionPlaceholder: "مثال: نموذج تفكير متقدم",
        descriptionHelp: "وصف موجز لقدرات النموذج",

        removeAiModel: "حذف نموذج الذكاء الاصطناعي",
        selectModelToRemove: "اختر النموذج المراد حذفه:",
        chooseModelToRemove: "اختر نموذجاً لحذفه...",
        removeModelWarning: "⚠️ تحذير: سيؤدي هذا إلى حذف النموذج نهائياً من قائمتك. يمكنك حذف أي نموذج بما في ذلك جميع النماذج الافتراضية.",
        
        allAiModels: "جميع نماذج الذكاء الاصطناعي",
        loadingModels: "تحميل النماذج...",
        
        modelTestingSimulation: "اختبار ومحاكاة النماذج",
        testContent: "محتوى الاختبار:",
        testContentPlaceholder: "أدخل محتوى الاختبار لإنشاء الأسئلة...",
        testContentDefault: "القلب البشري له أربع حجرات: أذينان وبطينان. يتدفق الدم من الأذين الأيمن إلى البطين الأيمن، ثم إلى الرئتين للأكسجة.",
        questionType: "نوع السؤال:",
        multipleChoiceMcq: "متعدد الخيارات (MCQ)",
        trueFalseTf: "صح أو خطأ (TF)",
        questionCount: "عدد الأسئلة:",
        startTestingAllModels: "بدء اختبار جميع النماذج",
        stopTesting: "إيقاف الاختبار",
        clearRateLimits: "مسح حدود المعدل",
        clickStartTesting: "انقر \"بدء الاختبار\" لاختبار جميع النماذج المتاحة",
        exportResults: "تصدير النتائج",
        
        // API Key Manager
        apiKeyManager: "مدير مفتاح API",
        currentApiKey: "حالة مفتاح API الحالي",
        newApiKey: "تحديث مفتاح API",
        enterNewApiKey: "مفتاح OpenRouter API الجديد:",
        updateApiKey: "تحديث مفتاح API",
        apiKeyUpdated: "تم تحديث مفتاح API بنجاح!",
        testResults: "نتائج الاختبار",
        quickInstructions: "تعليمات سريعة",

        // History & Statistics
        quizHistory: "تاريخ الاختبارات",
        clearHistory: "مسح التاريخ",
        dateRange: "نطاق التاريخ",
        allTypes: "جميع الأنواع",
        allTime: "كل الأوقات",
        today: "اليوم",
        thisWeek: "هذا الأسبوع",
        thisMonth: "هذا الشهر",
        totalQuizzes: "إجمالي الاختبارات",
        averageScore: "متوسط النتيجة",
        averageTime: "متوسط الوقت",
        loadingQuizHistory: "تحميل تاريخ الاختبارات...",
        savedQuizzes: "الاختبارات المحفوظة",
        completedQuizzes: "الاختبارات المكتملة",
        loadingSavedQuizzes: "تحميل الاختبارات المحفوظة...",
        takeQuiz: "خذ الاختبار",
        deleteSavedQuiz: "حذف",
        noSavedQuizzes: "لا توجد اختبارات محفوظة",
        clearAllSaved: "مسح الكل",
        saveQuizTitle: "حفظ الاختبار",
        saveQuizSubtitle: "أعط اختبارك اسماً لا يُنسى",
        quizNameLabel: "اسم الاختبار:",
        quizNameHint: "اتركه فارغاً لاستخدام الاسم الافتراضي",
        saveQuiz: "حفظ الاختبار",
        cancel: "إلغاء",
        source: "المصدر",
        type: "النوع",
        questions: "الأسئلة",
        quizSummaryText: "أنت على وشك حفظ اختبار",
        quizSummaryWith: "يحتوي على",
        quizSummaryQuestions: "أسئلة من",

        statistics: "الإحصائيات",
        exportStats: "تصدير الإحصائيات",
        overallScore: "النتيجة الإجمالية",
        acrossAllQuizzes: "عبر جميع الاختبارات",
        currentStreak: "السلسلة الحالية",
        daysInARow: "أيام متتالية",
        performanceMetrics: "مقاييس الأداء",
        totalQuestions: "إجمالي الأسئلة",
        correctAnswers: "الإجابات الصحيحة",
        incorrectAnswers: "الإجابات الخاطئة",
        questionTypes: "أنواع الأسئلة",
        mcqQuizzes: "اختبارات متعددة الخيارات",
        trueFalseQuizzes: "اختبارات صح أو خطأ",
        activity: "النشاط",
        quizzesToday: "اختبارات اليوم",

        // Quiz Results
        quizResults: "نتائج الاختبار",
        newQuiz: "اختبار جديد",
        reviewAnswers: "مراجعة الإجابات",
        saveQuiz: "حفظ الاختبار للاحقاً",

        // Dynamic content translations
        score: "النتيجة",
        correct: "صحيح",
        duration: "المدة",
        questions: "الأسئلة",

        // Status Messages
        loading: "جاري التحميل...",
        success: "نجح",
        error: "خطأ",
        warning: "تحذير",
        info: "معلومات"
    }
};

// Current language state
let currentLanguage = localStorage.getItem('language') || 'en';

// Translation function
function t(key) {
    return translations[currentLanguage][key] || translations['en'][key] || key;
}

// Update all text elements with translations
function updateLanguage() {
    // Update language toggle button
    const languageToggle = document.getElementById('languageToggle');
    if (languageToggle) {
        const languageText = languageToggle.querySelector('.language-text');
        if (languageText) {
            languageText.textContent = currentLanguage === 'en' ? 'عربي' : 'English';
        }
        languageToggle.title = t('switchLanguage');
    }

    // Update document language but keep direction as LTR to preserve layout
    document.documentElement.dir = 'ltr'; // Always keep LTR layout
    document.documentElement.lang = currentLanguage;

    // Update all elements with data-translate attribute
    document.querySelectorAll('[data-translate]').forEach(element => {
        const key = element.getAttribute('data-translate');
        if (element.tagName === 'INPUT' && (element.type === 'text' || element.type === 'textarea')) {
            element.placeholder = t(key);
        } else if (element.tagName === 'TEXTAREA') {
            element.placeholder = t(key);
        } else {
            element.textContent = t(key);
        }
    });

    // Update specific elements by ID or class
    updateElementText('#themeToggle', '', 'title', t('toggleTheme'));
    updateElementText('#historyBtn', '', 'title', t('history'));
    updateElementText('#statsBtn', '', 'title', t('statistics'));
    updateElementText('#headerBackBtn', '', 'title', t('backToMain'));

    // Update model select auto option
    const autoOption = document.querySelector('#modelSelect option[value="auto"]');
    if (autoOption) {
        autoOption.textContent = t('autoBestAvailable');
    }

    // Update status text if visible
    const statusText = document.querySelector('.status-text');
    if (statusText && statusText.textContent.includes('Checking') || statusText.textContent.includes('فحص')) {
        statusText.textContent = t('checkingAvailability');
    }

    const statusDescription = document.querySelector('.status-description');
    if (statusDescription && (statusDescription.textContent.includes('Model status') || statusDescription.textContent.includes('ستظهر'))) {
        statusDescription.textContent = t('modelStatusWillAppear');
    }

    // Update dynamic content that might be set by JavaScript
    updateDynamicContent();

    // Save language preference
    localStorage.setItem('language', currentLanguage);
}

// Update dynamic content that gets set by JavaScript
function updateDynamicContent() {
    // Update content screen title if it exists and has been set
    const contentScreenTitle = document.getElementById('contentScreenTitle');
    if (contentScreenTitle && contentScreenTitle.textContent.includes('Add Content')) {
        // Check if it's for MCQ or TF
        if (contentScreenTitle.textContent.includes('MCQ')) {
            contentScreenTitle.textContent = t('addContent') + ' ' + t('multipleChoice');
        } else if (contentScreenTitle.textContent.includes('TF')) {
            contentScreenTitle.textContent = t('addContent') + ' ' + t('trueFalse');
        } else {
            contentScreenTitle.textContent = t('addContent');
        }
    }

    // Update question number and count in quiz if visible
    const questionNumber = document.getElementById('questionNumber');
    if (questionNumber && questionNumber.textContent.includes('Question')) {
        const match = questionNumber.textContent.match(/(\d+)/);
        if (match) {
            questionNumber.innerHTML = `<span data-translate="question">${t('question')}</span> ${match[1]}`;
        }
    }

    const questionCount = document.getElementById('questionCount');
    if (questionCount && questionCount.textContent.includes('of')) {
        const match = questionCount.textContent.match(/(\d+)/);
        if (match) {
            questionCount.innerHTML = `<span data-translate="of">${t('of')}</span> ${match[1]}`;
        }
    }

    // Update score label
    const scoreElement = document.querySelector('.quiz-score span:first-child');
    if (scoreElement && scoreElement.textContent.includes('Score')) {
        scoreElement.textContent = t('score') + ': ';
    }
}

// Helper function to update element text
function updateElementText(selector, text, attribute = null, value = null) {
    const element = document.querySelector(selector);
    if (element) {
        if (attribute) {
            element.setAttribute(attribute, value);
        } else if (text) {
            element.textContent = text;
        }
    }
}

// Toggle language function
function toggleLanguage() {
    currentLanguage = currentLanguage === 'en' ? 'ar' : 'en';
    updateLanguage();
}

// Initialize language system
function initializeLanguage() {
    updateLanguage();
    
    // Add event listener to language toggle button
    const languageToggle = document.getElementById('languageToggle');
    if (languageToggle) {
        languageToggle.addEventListener('click', toggleLanguage);
    }
}

// Export functions for use in other files
window.t = t;
window.updateLanguage = updateLanguage;
window.updateDynamicContent = updateDynamicContent;
window.toggleLanguage = toggleLanguage;
window.initializeLanguage = initializeLanguage;
window.currentLanguage = () => currentLanguage;
